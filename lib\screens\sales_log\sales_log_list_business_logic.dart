import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/sales_log.dart';
import '../../models/sales_log_display_item.dart';
import '../../models/transaction_type.dart';
import '../../providers/sales_log_provider.dart';
import '../../utils/toast_utils.dart';
import '../../widgets/sales_record_detail_dialog.dart';

import 'sales_log_list_filter_logic.dart';
import 'sales_log_list_ui_components.dart';


/// 판매 기록 목록의 비즈니스 로직을 담당하는 클래스
///
/// 주요 기능:
/// - 판매 기록 삭제 (재고 복구 포함)
/// - 그룹 판매 기록 삭제
/// - 그룹 상세 다이얼로그 관리
/// - 필터링된 데이터 관리
class SalesLogListBusinessLogic {
  /// 필터링된 표시 아이템 목록 생성
  ///
  /// [ref]: Riverpod ref
  /// [selectedSeller]: 선택된 판매자
  /// [selectedTransactionType]: 선택된 거래 유형
  /// [selectedDateRange]: 선택된 날짜 범위
  /// 반환값: 필터링된 표시 아이템 목록
  static List<SalesLogDisplayItem> getFilteredDisplayItems({
    required WidgetRef ref,
    required String selectedSeller,
    required TransactionType? selectedTransactionType,
    required DateTimeRange? selectedDateRange,
  }) {
    final allDisplayItems = ref.watch(salesLogDisplayItemsProvider);
    
    return SalesLogListFilterLogic.getFilteredDisplayItems(
      allDisplayItems: allDisplayItems,
      selectedSeller: selectedSeller,
      selectedTransactionType: selectedTransactionType,
      selectedDateRange: selectedDateRange,
    );
  }

  /// 판매 기록 삭제 (재고 복구 포함)
  ///
  /// [ref]: Riverpod ref
  /// [context]: BuildContext
  /// [salesLog]: 삭제할 판매 기록
  /// 반환값: 삭제 성공 여부
  static Future<bool> deleteSalesLogComplete({
    required WidgetRef ref,
    required BuildContext context,
    required SalesLog salesLog,
  }) async {
    try {
      // 삭제 확인 다이얼로그 표시
      final shouldDelete = await SalesLogListUiComponents.showDeleteConfirmDialog(
        context,
        salesLog,
      );

      if (!shouldDelete) {
        return false;
      }

      // 완전한 판매 기록 삭제 (재고 복구 + Firebase 동기화 포함)
      final result = await ref
          .read(salesLogNotifierProvider.notifier)
          .deleteSalesLogComplete(salesLog);
    
      // 단순화된 데이터 갱신
      await ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();

      // 결과 메시지 표시
      if (context.mounted) {
        ToastUtils.showToast(
          context,
          result,
          duration: ToastUtils.shortDuration,
        );
      }

      return true;
    } catch (e) {
      // 오류 메시지 표시
      if (context.mounted) {
        ToastUtils.showError(
          context,
          '삭제 중 오류가 발생했습니다: $e',
        );
      }
      return false;
    }
  }

  /// 그룹 판매 기록 삭제 (재고 복구 포함)
  ///
  /// [ref]: Riverpod ref
  /// [context]: BuildContext
  /// [groupedSale]: 삭제할 그룹 판매 기록
  /// 반환값: 삭제 성공 여부
  static Future<bool> deleteGroupSalesLogWithStockRestore({
    required WidgetRef ref,
    required BuildContext context,
    required GroupedSale groupedSale,
  }) async {
    try {
      // 삭제 확인 다이얼로그 표시
      final shouldDelete = await SalesLogListUiComponents.showDeleteGroupConfirmDialog(
        context,
        groupedSale,
      );

      if (!shouldDelete) {
        return false;
      }

      // 그룹 판매 기록 삭제 및 재고 복구 (SalesLogCrud에서 처리)
      final result = await ref
          .read(salesLogNotifierProvider.notifier)
          .deleteGroupedSaleAndUpdateStock(groupedSale);

      // 단순화된 데이터 갱신
      await ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();

      // 결과 메시지 표시
      if (context.mounted) {
        ToastUtils.showToast(
          context,
          result,
          duration: ToastUtils.shortDuration,
        );
      }

      return true;
    } catch (e) {
      // 오류 메시지 표시
      if (context.mounted) {
        ToastUtils.showError(
          context,
          '그룹 삭제 중 오류가 발생했습니다: $e',
        );
      }
      return false;
    }
  }

  /// 그룹 상세 다이얼로그 표시
  ///
  /// [context]: BuildContext
  /// [groupedSale]: 표시할 그룹 판매 기록
  /// [selectedSeller]: 선택된 판매자 (필터링용)
  /// [onItemDelete]: 개별 아이템 삭제 콜백
  /// [productCategoryMap]: 상품ID -> 카테고리명 매핑 (선택사항)
  static void showGroupDetailDialog({
    required BuildContext context,
    required GroupedSale groupedSale,
    required String selectedSeller,
    required Function(SalesLog) onItemDelete,
    Map<int, String>? productCategoryMap,
  }) {
    SalesRecordDetailDialog.show(
      context: context,
      salesLogs: groupedSale.items,
      selectedSeller: selectedSeller,
      onItemDelete: onItemDelete,
      productCategoryMap: productCategoryMap,
    );
  }
}
