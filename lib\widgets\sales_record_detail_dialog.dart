import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../models/sales_log.dart';
import '../../utils/app_colors.dart';
import '../../utils/currency_utils.dart';
import '../../utils/toast_utils.dart';
import '../../widgets/confirmation_dialog.dart';

/// 통합된 판매 기록 상세 다이얼로그
/// 
/// 단일 및 다중 판매 기록을 동일한 리스트 UI로 표시합니다.
/// 상품이 1개든 여러 개든 일관된 사용자 경험을 제공합니다.
class SalesRecordDetailDialog extends ConsumerStatefulWidget {
  final List<SalesLog> salesLogs;
  final String? selectedSeller;
  final Map<int, String>? productCategoryMap;
  final Function(SalesLog)? onItemDelete;

  const SalesRecordDetailDialog({
    super.key,
    required this.salesLogs,
    this.selectedSeller,
    this.productCategoryMap,
    this.onItemDelete,
  });

  /// 다이얼로그 표시 메서드
  static Future<void> show({
    required BuildContext context,
    required List<SalesLog> salesLogs,
    String? selectedSeller,
    Map<int, String>? productCategoryMap,
    Function(SalesLog)? onItemDelete,
  }) {
    return showDialog(
      context: context,
      builder: (context) => SalesRecordDetailDialog(
        salesLogs: salesLogs,
        selectedSeller: selectedSeller,
        productCategoryMap: productCategoryMap,
        onItemDelete: onItemDelete,
      ),
    );
  }

  @override
  ConsumerState<SalesRecordDetailDialog> createState() => _SalesRecordDetailDialogState();
}

class _SalesRecordDetailDialogState extends ConsumerState<SalesRecordDetailDialog> {
  late List<SalesLog> currentSalesLogs;

  @override
  void initState() {
    super.initState();
    currentSalesLogs = List.from(widget.salesLogs);
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isTablet = screenWidth > 600;

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(
        horizontal: isTablet ? screenWidth * 0.15 : 16,
        vertical: screenHeight * 0.1,
      ),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: screenHeight * 0.8,
          maxWidth: isTablet ? 600 : double.infinity,
        ),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: _buildView(isTablet),
      ),
    );
  }

  /// 판매 기록 뷰 (단일/다중 통합)
  Widget _buildView(bool isTablet) {
    final totalQuantity = currentSalesLogs.fold<int>(0, (sum, log) => sum + log.soldQuantity);
    final totalAmount = currentSalesLogs.fold<int>(0, (sum, log) => sum + log.totalAmount);
    final totalSetDiscount = currentSalesLogs.fold<int>(0, (sum, log) => sum + log.setDiscountAmount);
    
    return Column(
      children: [
        // 헤더
        _buildHeader(
          isTablet: isTablet,
          title: '판매 상세',
          icon: Icons.shopping_cart,
          subtitle: '총 $totalQuantity개 • ${CurrencyUtils.formatCurrency(totalAmount)}',
          hasSetDiscount: totalSetDiscount > 0,
          setDiscountAmount: totalSetDiscount,
        ),
        
        // 날짜시간 정보
        _buildDateTimeInfo(isTablet),
        
        // 상품 목록
        Expanded(
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: isTablet ? 24.0 : 20.0,
              vertical: isTablet ? 16.0 : 12.0,
            ),
            child: ListView.builder(
              itemCount: currentSalesLogs.length,
              itemBuilder: (context, index) => _buildMultipleItem(
                currentSalesLogs[index], 
                index, 
                isTablet,
                currentSalesLogs.length, // 전체 개수 전달
              ),
            ),
          ),
        ),
        
        // 버튼
        _buildButtons(isTablet),
      ],
    );
  }

  /// 공통 헤더
  Widget _buildHeader({
    required bool isTablet,
    required String title,
    required IconData icon,
    required String subtitle,
    bool hasSetDiscount = false,
    int setDiscountAmount = 0,
  }) {
    return Container(
      padding: EdgeInsets.all(isTablet ? 24.0 : 20.0),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primarySeed,
            AppColors.primarySeed.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          // 아이콘
          Container(
            width: isTablet ? 48.0 : 40.0,
            height: isTablet ? 48.0 : 40.0,
            decoration: BoxDecoration(
              color: AppColors.onPrimary.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(isTablet ? 12.0 : 10.0),
            ),
            child: Icon(
              icon,
              color: AppColors.onPrimary,
              size: isTablet ? 24.0 : 20.0,
            ),
          ),
          
          SizedBox(width: isTablet ? 16.0 : 12.0),
          
          // 제목과 부제목
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: isTablet ? 20.0 : 18.0,
                    fontWeight: FontWeight.bold,
                    color: AppColors.onPrimary,
                    fontFamily: 'Pretendard',
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Flexible(
                      child: Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: isTablet ? 14.0 : 12.0,
                          fontWeight: FontWeight.w500,
                          color: AppColors.onPrimary.withValues(alpha: 0.9),
                          fontFamily: 'Pretendard',
                        ),
                      ),
                    ),
                    // 세트 할인 표시
                    if (hasSetDiscount) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: AppColors.onPrimary.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.local_offer,
                              size: 12,
                              color: AppColors.onPrimary,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '세트 -${CurrencyUtils.formatCurrency(setDiscountAmount)}',
                              style: TextStyle(
                                fontFamily: 'Pretendard',
                                fontSize: 11,
                                color: AppColors.onPrimary,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
          
          // 닫기 버튼
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Icon(Icons.close, color: AppColors.onPrimary),
            style: IconButton.styleFrom(
              backgroundColor: AppColors.onPrimary.withValues(alpha: 0.2),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 날짜시간 정보 섹션
  Widget _buildDateTimeInfo(bool isTablet) {
    if (currentSalesLogs.isEmpty) return const SizedBox.shrink();
    
    final firstSaleDateTime = DateTime.fromMillisecondsSinceEpoch(
      currentSalesLogs.first.saleTimestamp
    );
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        horizontal: isTablet ? 24.0 : 20.0,
        vertical: isTablet ? 12.0 : 10.0,
      ),
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey[200]!,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.schedule,
            size: 16,
            color: AppColors.onSurfaceVariant,
          ),
          const SizedBox(width: 8),
          Text(
            _formatFullDateTime(firstSaleDateTime),
            style: TextStyle(
              fontSize: 14,
              color: AppColors.onSurfaceVariant,
              fontFamily: 'Pretendard',
            ),
          ),
        ],
      ),
    );
  }

  /// 다중 판매 항목
  Widget _buildMultipleItem(SalesLog item, int index, bool isTablet, int totalCount) {
    final isCurrentSeller = widget.selectedSeller == '전체 판매자' || 
                           (item.sellerName ?? '알 수 없음') == widget.selectedSeller;

    final isFirst = index == 0;

    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(
          top: isFirst ? BorderSide(color: Colors.grey[300]!, width: 1) : BorderSide.none,
          left: BorderSide(color: Colors.grey[300]!, width: 1),
          right: BorderSide(color: Colors.grey[300]!, width: 1),
          bottom: BorderSide(color: Colors.grey[300]!, width: 1),
        ),
        borderRadius: BorderRadius.zero, // 사각형으로 변경
      ),
      child: Opacity(
        opacity: isCurrentSeller ? 1.0 : 0.6,
        child: Padding(
          padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // 상품 정보
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 상품명
                      Text(
                        _buildProductDisplayName(item),
                        style: TextStyle(
                          fontFamily: 'Pretendard',
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                          color: isCurrentSeller
                              ? AppColors.onSurface
                              : AppColors.onSurfaceVariant,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      // 판매자 및 거래 유형
                      Text(
                        '${item.transactionType.displayName} • ${item.sellerName ?? '알 수 없음'}',
                        style: TextStyle(
                          fontFamily: 'Pretendard',
                          fontSize: 13,
                          color: isCurrentSeller
                              ? AppColors.onSurfaceVariant
                              : AppColors.neutral50,
                        ),
                      ),
                      // 할인 정보
                      if (item.setDiscountAmount > 0 || item.manualDiscountAmount > 0)
                        Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: isCurrentSeller
                                  ? AppColors.success.withValues(alpha: 0.1)
                                  : AppColors.neutral20,
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  item.setDiscountAmount > 0 ? Icons.local_offer : Icons.money_off,
                                  size: 12,
                                  color: isCurrentSeller
                                      ? AppColors.success
                                      : AppColors.neutral50,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  item.setDiscountAmount > 0
                                      ? '세트할인 -${CurrencyUtils.formatCurrency(item.setDiscountAmount)}'
                                      : '수동할인 -${CurrencyUtils.formatCurrency(item.manualDiscountAmount)}',
                                  style: TextStyle(
                                    fontFamily: 'Pretendard',
                                    fontSize: 11,
                                    color: isCurrentSeller
                                        ? AppColors.success
                                        : AppColors.neutral50,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                // 수량과 금액
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${item.soldQuantity}개',
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 13,
                        fontWeight: FontWeight.w600,
                        color: isCurrentSeller
                            ? AppColors.onSurfaceVariant
                            : AppColors.neutral50,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      CurrencyUtils.formatCurrency(item.totalAmount),
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: isCurrentSeller
                            ? AppColors.success
                            : AppColors.neutral50,
                      ),
                    ),
                  ],
                ),
                if (widget.onItemDelete != null) ...[
                  const SizedBox(width: 12),
                  // 개별 삭제 버튼
                  SizedBox(
                    width: 32,
                    height: 32,
                    child: IconButton(
                      onPressed: () async {
                        await _handleItemDelete(item, index);
                      },
                      icon: Icon(
                        Icons.delete_outline,
                        size: 16,
                        color: isCurrentSeller
                            ? AppColors.error
                            : AppColors.neutral50,
                      ),
                      padding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
    );
  }

  /// 버튼 영역
  Widget _buildButtons(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(isTablet ? 24.0 : 20.0),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
        border: Border(
          top: BorderSide(
            color: AppColors.neutral20,
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              padding: EdgeInsets.symmetric(
                horizontal: isTablet ? 32 : 24,
                vertical: isTablet ? 16 : 12,
              ),
              backgroundColor: AppColors.primarySeed,
              foregroundColor: AppColors.onPrimary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              '닫기',
              style: TextStyle(
                fontFamily: 'Pretendard',
                fontSize: isTablet ? 16 : 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 개별 아이템 삭제 처리
  Future<void> _handleItemDelete(SalesLog item, int index) async {
    final shouldDelete = await ConfirmationDialog.showDelete(
      context: context,
      title: '판매 기록 삭제',
      message: '\'${item.productName}\' 판매 기록을 삭제하시겠습니까?\n해당 상품의 재고가 ${item.soldQuantity}개만큼 복원됩니다.',
      confirmLabel: '삭제',
      cancelLabel: '취소',
    );

    if (shouldDelete == true) {
      // 삭제 실행
      if (widget.onItemDelete != null) {
        await widget.onItemDelete!(item);
      }

      // UI 업데이트
      setState(() {
        currentSalesLogs.removeAt(index);
      });

      // 항목이 모두 삭제되면 다이얼로그 닫기
      if (currentSalesLogs.isEmpty) {
        if (mounted) {
          Navigator.of(context).pop();
        }
        return;
      }

      // 토스트 표시
      if (mounted) {
        ToastUtils.showToast(
          context,
          '판매 기록이 삭제되었습니다.',
          duration: ToastUtils.shortDuration,
        );
      }
    }
  }

  /// 상품명에 카테고리 포함하여 표시
  String _buildProductDisplayName(SalesLog salesLog) {
    if (widget.productCategoryMap != null && salesLog.productId != null) {
      final categoryName = widget.productCategoryMap![salesLog.productId];
      if (categoryName != null) {
        return '$categoryName-${salesLog.productName}';
      }
    }
    return salesLog.productName;
  }

  /// 전체 날짜 시간 포맷팅 (요일 포함)
  String _formatFullDateTime(DateTime dateTime) {
    const weekdays = ['월요일', '화요일', '수요일', '목요일', '금요일', '토요일', '일요일'];
    final weekday = weekdays[dateTime.weekday - 1];
    return '${dateTime.year}년 ${dateTime.month}월 ${dateTime.day}일 ($weekday) ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
